# 自定义插件开发示例

## 1. 创建一个简单的自定义插件

### 1.1 插件基础结构

```typescript
// src/my-custom-plugin/plugin.ts
import { Plugin, UniverInstanceType, Inject, Injector, IConfigService } from '@univerjs/core';
import { MyCustomService } from './services/my-custom.service';
import { MyCustomController } from './controllers/my-custom.controller';

const PLUGIN_NAME = 'MY_CUSTOM_PLUGIN';

export interface IMyCustomPluginConfig {
    enableFeatureA?: boolean;
    enableFeatureB?: boolean;
}

export const defaultPluginConfig: IMyCustomPluginConfig = {
    enableFeatureA: true,
    enableFeatureB: false,
};

export class MyCustomPlugin extends Plugin {
    static override pluginName = PLUGIN_NAME;
    static override type = UniverInstanceType.UNIVER_SHEET;

    constructor(
        private readonly _config: Partial<IMyCustomPluginConfig> = defaultPluginConfig,
        @Inject(Injector) protected readonly _injector: Injector,
        @IConfigService private readonly _configService: IConfigService
    ) {
        super();

        // 合并配置
        const config = { ...defaultPluginConfig, ...this._config };
        this._configService.setConfig('MY_CUSTOM_PLUGIN_CONFIG', config);
    }

    override onStarting(): void {
        // 注册服务和控制器
        this._injector.add([MyCustomService]);
        this._injector.add([MyCustomController]);
    }

    override onReady(): void {
        // 初始化控制器
        this._injector.get(MyCustomController);
    }
}
```

### 1.2 服务层实现

```typescript
// src/my-custom-plugin/services/my-custom.service.ts
import { Disposable, ICommandService } from '@univerjs/core';
import { BehaviorSubject } from 'rxjs';

export class MyCustomService extends Disposable {
    private _data$ = new BehaviorSubject<any[]>([]);
    readonly data$ = this._data$.asObservable();

    constructor(
        @ICommandService private readonly _commandService: ICommandService
    ) {
        super();
        this._initData();
    }

    private _initData(): void {
        // 初始化数据
        this._data$.next([]);
    }

    addData(item: any): void {
        const currentData = this._data$.getValue();
        this._data$.next([...currentData, item]);
    }

    removeData(index: number): void {
        const currentData = this._data$.getValue();
        const newData = currentData.filter((_, i) => i !== index);
        this._data$.next(newData);
    }

    getData(): any[] {
        return this._data$.getValue();
    }
}
```

### 1.3 控制器实现

```typescript
// src/my-custom-plugin/controllers/my-custom.controller.ts
import { Disposable, ICommandService, Inject } from '@univerjs/core';
import { MyCustomService } from '../services/my-custom.service';
import { MyCustomCommand } from '../commands/my-custom.command';

export class MyCustomController extends Disposable {
    constructor(
        @ICommandService private readonly _commandService: ICommandService,
        @Inject(MyCustomService) private readonly _myCustomService: MyCustomService
    ) {
        super();

        this._initCommands();
        this._initListeners();
    }

    private _initCommands(): void {
        // 注册自定义命令
        this.disposeWithMe(
            this._commandService.registerCommand(MyCustomCommand)
        );
    }

    private _initListeners(): void {
        // 监听数据变化
        this.disposeWithMe(
            this._myCustomService.data$.subscribe((data) => {
                console.log('Data changed:', data);
            })
        );
    }
}
```

### 1.4 命令实现

```typescript
// src/my-custom-plugin/commands/my-custom.command.ts
import { ICommand, CommandType } from '@univerjs/core';
import { MyCustomService } from '../services/my-custom.service';

export interface IMyCustomCommandParams {
    action: 'add' | 'remove';
    data?: any;
    index?: number;
}

export const MyCustomCommand: ICommand<IMyCustomCommandParams> = {
    id: 'my-custom.command',
    type: CommandType.COMMAND,

    handler: (accessor, params) => {
        const myCustomService = accessor.get(MyCustomService);
        const { action, data, index } = params;

        switch (action) {
            case 'add':
                if (data !== undefined) {
                    myCustomService.addData(data);
                    return true;
                }
                break;
            case 'remove':
                if (index !== undefined) {
                    myCustomService.removeData(index);
                    return true;
                }
                break;
        }

        return false;
    },
};
```

## 2. 带 UI 的插件示例

### 2.1 UI 插件主文件

```typescript
// src/my-custom-ui-plugin/plugin.ts
import { Plugin, UniverInstanceType, Inject, Injector, DependentOn } from '@univerjs/core';
import { IRenderManagerService } from '@univerjs/engine-render';
import { ComponentManager } from '@univerjs/ui';
import { MyCustomPlugin } from '../my-custom-plugin/plugin';
import { MyCustomUIController } from './controllers/my-custom-ui.controller';
import { MyCustomRenderController } from './views/my-custom-render.controller';

@DependentOn(MyCustomPlugin)
export class MyCustomUIPlugin extends Plugin {
    static override pluginName = 'MY_CUSTOM_UI_PLUGIN';
    static override type = UniverInstanceType.UNIVER_SHEET;

    constructor(
        @Inject(Injector) protected readonly _injector: Injector,
        @IRenderManagerService private readonly _renderManagerService: IRenderManagerService,
        @Inject(ComponentManager) private readonly _componentManager: ComponentManager
    ) {
        super();
    }

    override onStarting(): void {
        // 注册 UI 控制器
        this._injector.add([MyCustomUIController]);
    }

    override onReady(): void {
        // 注册渲染模块
        this.disposeWithMe(
            this._renderManagerService.registerRenderModule(
                UniverInstanceType.UNIVER_SHEET,
                [MyCustomRenderController]
            )
        );
    }

    override onRendered(): void {
        // 初始化 UI 控制器
        this._injector.get(MyCustomUIController);
    }
}
```

### 2.2 UI 控制器

```typescript
// src/my-custom-ui-plugin/controllers/my-custom-ui.controller.ts
import { Disposable, ICommandService, Inject } from '@univerjs/core';
import { ComponentManager, IMenuManagerService } from '@univerjs/ui';
import { MyCustomService } from '../../my-custom-plugin/services/my-custom.service';
import { MyCustomPanel } from '../views/components/MyCustomPanel';

export class MyCustomUIController extends Disposable {
    constructor(
        @ICommandService private readonly _commandService: ICommandService,
        @Inject(ComponentManager) private readonly _componentManager: ComponentManager,
        @IMenuManagerService private readonly _menuManagerService: IMenuManagerService,
        @Inject(MyCustomService) private readonly _myCustomService: MyCustomService
    ) {
        super();

        this._initUI();
        this._initMenus();
    }

    private _initUI(): void {
        // 注册 React 组件
        this.disposeWithMe(
            this._componentManager.register('MyCustomPanel', MyCustomPanel)
        );
    }

    private _initMenus(): void {
        // 注册菜单项
        const menuSchema = {
            [RibbonDataGroup.TOOLS]: {
                'my-custom.open-panel': {
                    order: 1,
                    menuItemFactory: () => ({
                        id: 'my-custom.open-panel',
                        title: 'Open My Panel',
                        icon: 'CustomIcon',
                        onClick: () => {
                            // 打开自定义面板
                            this._openCustomPanel();
                        },
                    }),
                },
            },
        };

        this.disposeWithMe(
            this._menuManagerService.mergeMenus(menuSchema)
        );
    }

    private _openCustomPanel(): void {
        // 实现打开面板的逻辑
        console.log('Opening custom panel...');
    }
}
```

### 2.3 React 组件

```typescript
// src/my-custom-ui-plugin/views/components/MyCustomPanel.tsx
import React, { useCallback, useEffect, useState } from 'react';
import { useDependency } from '@univerjs/core';
import { Button, Input } from '@univerjs/design';
import { MyCustomService } from '../../../my-custom-plugin/services/my-custom.service';

export function MyCustomPanel() {
    const myCustomService = useDependency(MyCustomService);
    const [data, setData] = useState<any[]>([]);
    const [inputValue, setInputValue] = useState('');

    useEffect(() => {
        const subscription = myCustomService.data$.subscribe(setData);
        return () => subscription.unsubscribe();
    }, [myCustomService]);

    const handleAdd = useCallback(() => {
        if (inputValue.trim()) {
            myCustomService.addData(inputValue.trim());
            setInputValue('');
        }
    }, [inputValue, myCustomService]);

    const handleRemove = useCallback((index: number) => {
        myCustomService.removeData(index);
    }, [myCustomService]);

    return (
        <div className="my-custom-panel">
            <div className="input-section">
                <Input
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="Enter data..."
                />
                <Button onClick={handleAdd}>Add</Button>
            </div>
            
            <div className="data-list">
                {data.map((item, index) => (
                    <div key={index} className="data-item">
                        <span>{item}</span>
                        <Button onClick={() => handleRemove(index)}>Remove</Button>
                    </div>
                ))}
            </div>
        </div>
    );
}
```

## 3. 插件使用示例

### 3.1 注册插件

```typescript
// main.ts
import { Univer } from '@univerjs/core';
import { MyCustomPlugin } from './my-custom-plugin/plugin';
import { MyCustomUIPlugin } from './my-custom-ui-plugin/plugin';

// 创建 Univer 实例
const univer = new Univer();

// 注册插件
univer.registerPlugin(MyCustomPlugin, {
    enableFeatureA: true,
    enableFeatureB: true,
});

univer.registerPlugin(MyCustomUIPlugin);

// 创建工作簿
const workbook = univer.createUniverSheet({});
```

### 3.2 使用插件功能

```typescript
// 获取插件服务
const myCustomService = univer.getInjector().get(MyCustomService);

// 使用服务
myCustomService.addData('Hello World');
myCustomService.addData('Univer Plugin');

// 监听数据变化
myCustomService.data$.subscribe((data) => {
    console.log('Current data:', data);
});

// 执行命令
const commandService = univer.getInjector().get(ICommandService);
commandService.executeCommand('my-custom.command', {
    action: 'add',
    data: 'New Item',
});
```

## 4. 插件开发最佳实践

### 4.1 目录结构建议

```
my-custom-plugin/
├── src/
│   ├── commands/           # 命令定义
│   ├── controllers/        # 控制器
│   ├── services/          # 服务层
│   ├── models/            # 数据模型
│   ├── views/             # UI 组件
│   │   ├── components/    # React 组件
│   │   └── render-modules/ # 渲染模块
│   ├── types/             # 类型定义
│   ├── locale/            # 国际化文件
│   └── plugin.ts          # 插件主文件
├── package.json
└── README.md
```

### 4.2 开发注意事项

1. **依赖管理**：明确声明插件依赖关系
2. **生命周期**：在正确的生命周期阶段执行相应操作
3. **资源清理**：实现 dispose 方法，避免内存泄漏
4. **错误处理**：添加适当的错误处理和日志记录
5. **类型安全**：使用 TypeScript 确保类型安全
6. **测试覆盖**：编写单元测试和集成测试

这个示例展示了如何创建一个完整的 Univer 插件，包括服务层、控制器、命令系统和 UI 组件。
