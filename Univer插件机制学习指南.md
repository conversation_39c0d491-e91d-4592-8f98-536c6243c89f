# Univer 插件机制学习指南

## 1. 插件系统架构概览

Univer 采用基于依赖注入的插件架构，核心组件包括：
- **Plugin 基类** - 所有插件的基础类
- **PluginService** - 插件管理服务
- **Injector** - 依赖注入容器
- **LifecycleService** - 生命周期管理

## 2. Plugin 基类定义

<augment_code_snippet path="packages/core/src/services/plugin/plugin.service.ts" mode="EXCERPT">
````typescript
/**
 * 插件基类，所有插件必须继承此基类
 */
export abstract class Plugin extends Disposable {
    static pluginName: string;                    // 插件名称
    static type: UnitType = UniverInstanceType.UNIVER_UNKNOWN;  // 插件类型

    protected abstract _injector: Injector;       // 依赖注入器

    // 生命周期钩子方法
    onStarting(): void { /* 插件启动时 */ }
    onReady(): void { /* 准备就绪时 */ }
    onRendered(): void { /* 渲染完成时 */ }
    onSteady(): void { /* 稳定状态时 */ }

    getUnitType(): UnitType { return (this.constructor as typeof Plugin).type; }
    getPluginName(): string { return (this.constructor as typeof Plugin).pluginName; }
}
````
</augment_code_snippet>

## 3. 生命周期阶段

<augment_code_snippet path="packages/core/src/services/lifecycle/lifecycle.ts" mode="EXCERPT">
````typescript
export enum LifecycleStages {
    Starting,   // 注册插件到 Univer
    Ready,      // 业务实例创建，服务初始化，准备首次渲染
    Rendered,   // 首次渲染完成
    Steady,     // 所有懒加载任务完成，应用完全就绪
}
````
</augment_code_snippet>

## 4. 筛选插件实现示例

### 4.1 核心筛选插件 (sheets-filter)

<augment_code_snippet path="packages/sheets-filter/src/plugin.ts" mode="EXCERPT">
````typescript
export class UniverSheetsFilterPlugin extends Plugin {
    static override type = UniverInstanceType.UNIVER_SHEET;
    static override pluginName = SHEET_FILTER_SNAPSHOT_ID;

    constructor(
        private readonly _config: Partial<IUniverSheetsFilterConfig> = defaultPluginConfig,
        @Inject(Injector) protected readonly _injector: Injector,
        @IConfigService private readonly _configService: IConfigService
    ) {
        super();

        // 配置管理
        const { ...rest } = merge({}, defaultPluginConfig, this._config);
        this._configService.setConfig(SHEETS_FILTER_PLUGIN_CONFIG_KEY, rest);
    }

    override onStarting(): void {
        // 注册依赖项
        ([
            [SheetsFilterFormulaService],
            [SheetsFilterService],
            [SheetsFilterController],
        ] as Dependency[]).forEach((d) => this._injector.add(d));
    }

    override onReady(): void {
        // 触发依赖项初始化
        touchDependencies(this._injector, [
            [SheetsFilterFormulaService],
            [SheetsFilterController],
        ]);
    }
}
````
</augment_code_snippet>

### 4.2 筛选 UI 插件 (sheets-filter-ui)

<augment_code_snippet path="packages/sheets-filter-ui/src/plugin.ts" mode="EXCERPT">
````typescript
@DependentOn(UniverSheetsFilterPlugin)  // 声明依赖关系
export class UniverSheetsFilterUIPlugin extends Plugin {
    static override type = UniverInstanceType.UNIVER_SHEET;
    static override pluginName = NAME;

    constructor(
        private readonly _config: Partial<IUniverSheetsFilterUIConfig> = defaultPluginConfig,
        @Inject(Injector) protected readonly _injector: Injector,
        @IConfigService private readonly _configService: IConfigService,
        @Optional(IRPCChannelService) private readonly _rpcChannelService?: IRPCChannelService
    ) {
        super();

        // 配置管理
        const { menu, ...rest } = merge({}, defaultPluginConfig, this._config);
        if (menu) {
            this._configService.setConfig('menu', menu, { merge: true });
        }
        this._configService.setConfig(SHEETS_FILTER_UI_PLUGIN_CONFIG_KEY, rest);
    }

    override onStarting(): void {
        // 注册核心服务
        registerDependencies(this._injector, [
            [SheetsFilterPanelService],
            [SheetsFilterPermissionController],
            [SheetsFilterUIDesktopController],
        ]);

        // 条件性注册远程服务
        if (this._config.useRemoteFilterValuesGenerator && this._rpcChannelService) {
            this._injector.add([ISheetsGenerateFilterValuesService, {
                useFactory: (): ISheetsGenerateFilterValuesService =>
                    toModule<ISheetsGenerateFilterValuesService>(
                        this._rpcChannelService!.requestChannel(SHEETS_GENERATE_FILTER_VALUES_SERVICE_NAME)
                    ),
            }]);
        }
    }

    override onReady(): void {
        // 权限控制器在 Ready 阶段初始化
        touchDependencies(this._injector, [
            [SheetsFilterPermissionController],
        ]);
    }

    override onRendered(): void {
        // UI 控制器在 Rendered 阶段初始化
        touchDependencies(this._injector, [
            [SheetsFilterUIDesktopController],
        ]);
    }
}
````
</augment_code_snippet>

## 5. 依赖注入机制

### 5.1 依赖注册方式

```typescript
// 方式1: 直接注册类
this._injector.add([ServiceClass]);

// 方式2: 注册接口实现
this._injector.add([IServiceInterface, { useClass: ServiceImplementation }]);

// 方式3: 工厂函数
this._injector.add([IService, {
    useFactory: () => new ServiceImplementation(config)
}]);

// 方式4: 懒加载
this._injector.add([IService, { useClass: ServiceClass, lazy: true }]);
```

### 5.2 依赖获取

```typescript
// 构造函数注入
constructor(@Inject(ServiceClass) private service: ServiceClass) {}

// 手动获取
const service = this._injector.get(ServiceClass);

// 触发依赖初始化
touchDependencies(this._injector, [[ServiceClass]]);
```

## 6. 控制器模式

<augment_code_snippet path="packages/sheets-filter-ui/src/controllers/sheets-filter-ui-desktop.controller.ts" mode="EXCERPT">
````typescript
export class SheetsFilterUIDesktopController extends SheetsFilterUIMobileController {
    constructor(
        @Inject(Injector) private readonly _injector: Injector,
        @Inject(ComponentManager) private readonly _componentManager: ComponentManager,
        @Inject(SheetsFilterPanelService) private readonly _sheetsFilterPanelService: SheetsFilterPanelService,
        // ... 其他依赖注入
    ) {
        super(renderManagerService, sheetsRenderService);

        // 初始化各个模块
        this._initCommands();    // 命令注册
        this._initShortcuts();   // 快捷键注册
        this._initMenuItems();   // 菜单项注册
        this._initUI();          // UI 组件注册
    }

    private _initCommands(): void {
        [
            SmartToggleSheetsFilterCommand,
            RemoveSheetFilterCommand,
            SetSheetFilterRangeCommand,
            // ... 更多命令
        ].forEach((command) => {
            this.disposeWithMe(this._commandService.registerCommand(command));
        });
    }

    private _initMenuItems(): void {
        this.disposeWithMe(this._menuManagerService.mergeMenus(menuSchema));
    }
}
````
</augment_code_snippet>

## 7. 插件依赖关系

### 7.1 依赖声明

```typescript
@DependentOn(UniverSheetsFilterPlugin)
export class UniverSheetsFilterUIPlugin extends Plugin {
    // UI 插件依赖于核心筛选插件
}
```

### 7.2 依赖解析流程

1. **依赖检查** - 检查所需依赖是否已注册
2. **自动注册** - 未注册的依赖会自动注册（使用默认配置）
3. **拓扑排序** - 按依赖关系排序插件加载顺序
4. **实例化** - 按正确顺序创建插件实例

## 8. 配置系统

### 8.1 配置定义

```typescript
export interface IUniverSheetsFilterUIConfig {
    menu?: IMenuConfig;
    useRemoteFilterValuesGenerator?: boolean;
}

export const defaultPluginConfig: IUniverSheetsFilterUIConfig = {
    useRemoteFilterValuesGenerator: false,
};
```

### 8.2 配置使用

```typescript
// 插件构造函数中合并配置
const { menu, ...rest } = merge({}, defaultPluginConfig, this._config);

// 注册到配置服务
this._configService.setConfig(SHEETS_FILTER_UI_PLUGIN_CONFIG_KEY, rest);
```

## 9. 渲染模块注册

```typescript
// 在 Ready 阶段注册渲染模块
override onReady(): void {
    this.disposeWithMe(
        this._renderManagerService.registerRenderModule(
            UniverInstanceType.UNIVER_SHEET,
            [SheetsFilterRenderController]
        )
    );
}
```

## 10. 最佳实践

### 10.1 插件设计原则
- **单一职责** - 每个插件专注特定功能
- **松耦合** - 通过接口和事件通信
- **可配置** - 提供灵活的配置选项
- **可扩展** - 支持功能扩展和覆盖

### 10.2 生命周期使用指南
- **onStarting** - 注册服务和依赖
- **onReady** - 初始化业务逻辑
- **onRendered** - 初始化 UI 相关功能
- **onSteady** - 执行懒加载任务

### 10.3 依赖管理
- 明确声明插件依赖关系
- 合理使用懒加载减少启动时间
- 避免循环依赖
- 使用接口抽象降低耦合

## 11. 插件注册和启动流程

### 11.1 插件注册

<augment_code_snippet path="packages/core/src/services/plugin/plugin.service.ts" mode="EXCERPT">
````typescript
export class PluginService extends Disposable {
    private _pluginRegistry = new Map<string, IPluginRegistryItem>();
    private _loadedPlugins = new Set<string>();

    /**
     * 注册插件到 Univer
     */
    registerPlugin<T extends PluginCtor>(ctor: T, config?: ConstructorParameters<T>[0]): void {
        this._assertPluginValid(ctor);

        const item = { plugin: ctor, options: config };
        this._pluginRegistry.set(ctor.pluginName, item);

        // 如果对应类型已加载，立即启动插件
        const { type } = ctor;
        if (this._loadedPluginTypes.has(type)) {
            if (type === UniverInstanceType.UNIVER_UNKNOWN) {
                this._loadFromPlugins([item]);
            } else {
                this._flushType(type);
            }
        }
    }

    /**
     * 启动指定类型的所有插件
     */
    startPluginsForType(type: UnitType): void {
        if (this._loadedPluginTypes.has(type)) return;
        this._loadPluginsForType(type);
    }
}
````
</augment_code_snippet>

### 11.2 依赖解析算法

<augment_code_snippet path="packages/core/src/services/plugin/plugin.service.ts" mode="EXCERPT">
````typescript
private _loadFromPlugins(plugins: IPluginRegistryItem[]): void {
    const finalPlugins: IPluginRegistryItem[] = [];

    // 深度优先搜索解析依赖
    const dfs = (item: IPluginRegistryItem) => {
        const { plugin, options } = item;
        const pluginName = plugin.pluginName;

        // 检查依赖项
        const dependencies = plugin[DependentOnSymbol];
        if (dependencies?.length) {
            dependencies.forEach((d) => {
                if (!this._pluginRegistry.has(d.pluginName)) {
                    // 自动注册未注册的依赖
                    this._assertPluginValid(d);
                    dfs({ plugin: d, options: undefined });
                }
            });
        }

        finalPlugins.push(item);
    };

    plugins.forEach((p) => dfs(p));

    // 创建插件实例并执行生命周期
    const pluginInstances = finalPlugins.map((p) => this._initPlugin(p.plugin, p.options));
    this._pluginsRunLifecycle(pluginInstances);
}
````
</augment_code_snippet>

## 12. 服务和控制器模式

### 12.1 服务层设计

<augment_code_snippet path="packages/sheets-filter/src/services/sheet-filter.service.ts" mode="EXCERPT">
````typescript
/**
 * 筛选服务 - 管理筛选模型的生命周期
 */
export class SheetsFilterService extends Disposable {
    private readonly _filterModels = new Map<string, Map<string, FilterModel>>();
    private readonly _loadedUnitId$ = new BehaviorSubject<Nullable<string>>(null);

    constructor(
        @IUniverInstanceService private readonly _univerInstanceService: IUniverInstanceService,
        @ICommandService private readonly _commandService: ICommandService
    ) {
        super();
        this._initCommandListeners();
    }

    /**
     * 确保筛选模型存在
     */
    ensureFilterModel(unitId: string, subUnitId: string): FilterModel {
        const already = this.getFilterModel(unitId, subUnitId);
        if (already) return already;

        const workbook = this._univerInstanceService.getUniverSheetInstance(unitId);
        if (!workbook) {
            throw new Error(`[SheetsFilterService]: could not create "FilterModel" on a non-existing workbook ${unitId}!`);
        }

        const worksheet = workbook.getSheetBySheetId(subUnitId);
        if (!worksheet) {
            throw new Error(`[SheetsFilterService]: could not create "FilterModel" on a non-existing worksheet ${subUnitId}!`);
        }

        const filterModel = new FilterModel(unitId, subUnitId, worksheet);
        this._cacheFilterModel(unitId, subUnitId, filterModel);
        return filterModel;
    }

    private _initCommandListeners(): void {
        // 监听命令执行，更新筛选状态
        this.disposeWithMe(
            this._commandService.onCommandExecuted((command) => {
                if (FILTER_MUTATIONS.has(command.id)) {
                    this._handleFilterMutation(command);
                }
            })
        );
    }
}
````
</augment_code_snippet>

### 12.2 控制器层设计

<augment_code_snippet path="packages/sheets-filter/src/controllers/sheets-filter.controller.ts" mode="EXCERPT">
````typescript
/**
 * 筛选控制器 - 协调筛选功能的各个组件
 */
export class SheetsFilterController extends Disposable {
    constructor(
        @IUniverInstanceService private readonly _univerInstanceService: IUniverInstanceService,
        @ICommandService private readonly _commandService: ICommandService,
        @Inject(SheetsFilterService) private readonly _sheetsFilterService: SheetsFilterService
    ) {
        super();

        this._initCommands();
        this._initMutations();
        this._initWorkbookListener();
    }

    private _initCommands(): void {
        // 注册筛选相关命令
        [
            SmartToggleSheetsFilterCommand,
            SetSheetFilterRangeCommand,
            RemoveSheetFilterCommand,
            SetSheetsFilterCriteriaCommand,
            ClearSheetsFilterCriteriaCommand,
            ReCalcSheetsFilterCommand,
        ].forEach((command) => {
            this.disposeWithMe(this._commandService.registerCommand(command));
        });
    }

    private _initMutations(): void {
        // 注册筛选相关变更操作
        [
            SetSheetsFilterRangeMutation,
            SetSheetsFilterCriteriaMutation,
            RemoveSheetsFilterMutation,
            ReCalcSheetsFilterMutation,
        ].forEach((mutation) => {
            this.disposeWithMe(this._commandService.registerCommand(mutation));
        });
    }
}
````
</augment_code_snippet>

## 13. 命令模式实现

### 13.1 命令定义

<augment_code_snippet path="packages/sheets-filter/src/commands/commands/sheets-filter.command.ts" mode="EXCERPT">
````typescript
/**
 * 智能切换筛选命令
 */
export const SmartToggleSheetsFilterCommand: ICommand<ISheetCommandSharedParams> = {
    id: 'sheet.command.smart-toggle-filter',
    type: CommandType.COMMAND,

    handler: (accessor, params) => {
        const sheetsFilterService = accessor.get(SheetsFilterService);
        const commandService = accessor.get(ICommandService);
        const instanceSrv = accessor.get(IUniverInstanceService);

        const commandTarget = getSheetCommandTarget(instanceSrv, params);
        if (!commandTarget) return false;

        const { unitId, subUnitId } = commandTarget;
        const filterModel = sheetsFilterService.getFilterModel(unitId, subUnitId);

        if (filterModel) {
            // 如果已有筛选，则移除
            return commandService.executeCommand(RemoveSheetFilterCommand.id, { unitId, subUnitId });
        } else {
            // 如果没有筛选，则创建
            return commandService.executeCommand(SetSheetFilterRangeCommand.id, { unitId, subUnitId });
        }
    },
};
````
</augment_code_snippet>

### 13.2 变更操作 (Mutation)

<augment_code_snippet path="packages/sheets-filter/src/commands/mutations/sheets-filter.mutation.ts" mode="EXCERPT">
````typescript
/**
 * 设置筛选范围变更操作
 */
export const SetSheetsFilterRangeMutation: IMutation<ISetSheetsFilterRangeMutationParams> = {
    id: 'sheet.mutation.set-filter-range',
    type: CommandType.MUTATION,

    handler: (accessor, params) => {
        const { unitId, subUnitId, range } = params;
        const sheetsFilterService = accessor.get(SheetsFilterService);

        const filterModel = sheetsFilterService.ensureFilterModel(unitId, subUnitId);
        filterModel.setRange(range);

        return true;
    },
};
````
</augment_code_snippet>

## 14. 事件系统和响应式编程

### 14.1 RxJS 在插件中的应用

<augment_code_snippet path="packages/sheets-filter-ui/src/views/render-modules/sheets-filter.render-controller.ts" mode="EXCERPT">
````typescript
export class SheetsFilterRenderController extends RxDisposable implements IRenderModule {
    private _initRenderer(): void {
        // 响应式监听骨架变化和筛选模型变化
        this._sheetSkeletonManagerService.currentSkeleton$.pipe(
            switchMap((skeletonParams) => {
                if (!skeletonParams) return of(null);

                const getParams = (): ISheetsFilterRenderParams => ({
                    unitId, worksheetId, filterModel, range: filterModel?.getRange(), skeleton: skeletonParams.skeleton,
                });

                // 监听命令执行，过滤筛选相关的变更
                return fromCallback(this._commandService.onCommandExecuted.bind(this._commandService)).pipe(
                    filter(([command]) =>
                        command.type === CommandType.MUTATION
                        && (command.params as Partial<ISheetCommandSharedParams>)?.unitId === workbook.getUnitId()
                        && (FILTER_MUTATIONS.has(command.id) || command.id === SetRangeValuesMutation.id)
                    ),
                    throttleTime(20, undefined, { leading: false, trailing: true }),
                    map(getParams),
                    startWith(getParams())
                );
            }),
            takeUntil(this.dispose$)
        ).subscribe((renderParams) => {
            this._disposeRendering();
            if (!renderParams || !renderParams.range) return;

            this._renderRange(renderParams.range, renderParams.skeleton);
            this._renderButtons(renderParams as Required<ISheetsFilterRenderParams>);
        });
    }
}
````
</augment_code_snippet>

## 15. 国际化和主题系统

### 15.1 国际化支持

```typescript
// 在控制器中注入 LocaleService
constructor(@Inject(LocaleService) private _localeService: LocaleService) {}

// 使用国际化文本
const filterText = this._localeService.t('sheets-filter.panel.filter-only');
```

### 15.2 主题系统

```typescript
// 在渲染组件中使用主题
constructor(@Inject(ThemeService) private readonly _themeService: ThemeService) {}

// 获取主题颜色
const fgColor = this._themeService.getColorFromTheme('primary.600');
const bgColor = this._themeService.getColorFromTheme('gray.50');
```

这套插件机制通过依赖注入、生命周期管理、命令模式、事件系统和模块化设计，实现了高度可扩展和可维护的架构。每个插件都可以独立开发、测试和部署，同时通过统一的接口和协议进行协作。
