# 筛选图标绘制和事件绑定实现梳理

## 概述

Univer 中的筛选功能包含两套图标系统：
1. **普通筛选图标** - 用于 sheets-filter 功能
2. **表格筛选图标** - 用于 sheets-table 功能

## 1. 普通筛选图标实现

### 1.1 核心文件结构
```
packages/sheets-filter-ui/src/views/
├── widgets/
│   ├── filter-button.shape.ts          # 筛选按钮形状定义
│   └── drawings.ts                      # 图标绘制逻辑
└── render-modules/
    └── sheets-filter.render-controller.ts  # 渲染控制器
```

### 1.2 图标绘制逻辑 (drawings.ts)

<augment_code_snippet path="packages/sheets-filter-ui/src/views/widgets/drawings.ts" mode="EXCERPT">
````typescript
export class FilterButton {
    // 绘制无筛选条件的图标（三条横线）
    static drawNoCriteria(ctx: UniverRenderingContext2D, size: number, fgColor: string, bgColor: string): void {
        // 绘制背景圆角矩形
        Rect.drawWith(ctx, { radius: 2, width: BUTTON_VIEWPORT, height: BUTTON_VIEWPORT, fill: bgColor });
        
        // 绘制三条横线（筛选图标）
        ctx.moveTo(3, 4); ctx.lineTo(13, 4);    // 第一条线
        ctx.moveTo(4.5, 8); ctx.lineTo(11.5, 8); // 第二条线
        ctx.moveTo(6, 12); ctx.lineTo(10, 12);   // 第三条线
    }

    // 绘制有筛选条件的图标（漏斗形状）
    static drawHasCriteria(ctx: UniverRenderingContext2D, size: number, fgColor: string, bgColor: string): void {
        // 绘制背景
        Rect.drawWith(ctx, { radius: 2, width: BUTTON_VIEWPORT, height: BUTTON_VIEWPORT, fill: bgColor });
        
        // 绘制漏斗形状
        ctx.fill(FILTER_BUTTON_EMPTY); // 使用预定义的漏斗路径
    }
}
````
</augment_code_snippet>

### 1.3 按钮形状定义 (filter-button.shape.ts)

<augment_code_snippet path="packages/sheets-filter-ui/src/views/widgets/filter-button.shape.ts" mode="EXCERPT">
````typescript
export class SheetsFilterButtonShape extends Shape {
    constructor(key: string, props: ISheetsFilterButtonShapeProps) {
        super(key, props);
        
        // 绑定鼠标事件
        this.onPointerDown$.subscribeEvent((evt) => this.onPointerDown(evt));
        this.onPointerEnter$.subscribeEvent(() => this.onPointerEnter());
        this.onPointerLeave$.subscribeEvent(() => this.onPointerLeave());
    }

    // 绘制方法
    protected override _draw(ctx: UniverRenderingContext2D): void {
        const { hasCriteria } = this._filterParams!;
        
        if (hasCriteria) {
            FilterButton.drawHasCriteria(ctx, FILTER_ICON_SIZE, fgColor, bgColor);
        } else {
            FilterButton.drawNoCriteria(ctx, FILTER_ICON_SIZE, fgColor, bgColor);
        }
    }

    // 点击事件处理
    onPointerDown(evt: IPointerEvent | IMouseEvent): void {
        const { col, unitId, subUnitId } = this._filterParams!;
        
        // 执行打开筛选面板的操作
        this._commandService.executeCommand(OpenFilterPanelOperation.id, {
            unitId, subUnitId, col
        });
    }
}
````
</augment_code_snippet>

### 1.4 渲染控制器 (sheets-filter.render-controller.ts)

<augment_code_snippet path="packages/sheets-filter-ui/src/views/render-modules/sheets-filter.render-controller.ts" mode="EXCERPT">
````typescript
export class SheetsFilterRenderController extends RxDisposable implements IRenderModule {
    private _renderButtons(params: Required<ISheetsFilterRenderParams>): void {
        const { range, filterModel, unitId, skeleton, worksheetId } = params;
        
        // 为每一列创建筛选按钮
        for (let col = startColumn; col <= endColumn; col++) {
            const startPosition = getCoordByCell(startRow, col, scene, skeleton);
            const { startX, startY, endX, endY } = startPosition;
            
            // 计算图标位置（右上角）
            const iconStartX = endX - FILTER_ICON_SIZE - FILTER_ICON_PADDING;
            const iconStartY = endY - FILTER_ICON_SIZE - FILTER_ICON_PADDING;
            
            // 检查是否有筛选条件
            const hasCriteria = !!filterModel.getFilterColumn(col);
            
            // 创建按钮形状
            const buttonShape = this._injector.createInstance(SheetsFilterButtonShape, key, props);
            this._filterButtonShapes.push(buttonShape);
        }
        
        // 添加到场景并标记为脏
        scene.addObjects(this._filterButtonShapes);
        scene.makeDirty();
    }

    // 拦截单元格内容，为筛选按钮留出空间
    private _interceptCellContent(workbookId: string, worksheetId: string, range: IRange): void {
        this._sheetInterceptorService.intercept(INTERCEPTOR_POINT.CELL_CONTENT, {
            handler: (cell, pos, next) => {
                // 为筛选按钮预留右侧空间
                cell.fontRenderExtension = {
                    ...cell?.fontRenderExtension,
                    rightOffset: FILTER_ICON_SIZE,
                };
                return next(cell);
            }
        });
    }
}
````
</augment_code_snippet>

## 2. 表格筛选图标实现

### 2.1 核心文件结构
```
packages/sheets-table-ui/src/views/widgets/
├── table-filter-button.shape.ts        # 表格筛选按钮形状
├── drawings.ts                          # 表格按钮绘制逻辑
└── icons.ts                            # 图标路径定义
```

### 2.2 图标状态枚举

表格筛选支持多种状态的图标：
- `FilteredSortNone` - 已筛选，无排序
- `FilteredSortAsc` - 已筛选，升序
- `FilteredSortDesc` - 已筛选，降序
- `FilterNoneSortAsc` - 无筛选，升序
- `FilterNoneSortDesc` - 无筛选，降序

### 2.3 表格按钮绘制 (drawings.ts)

<augment_code_snippet path="packages/sheets-table-ui/src/views/widgets/drawings.ts" mode="EXCERPT">
````typescript
export class TableButton {
    // 绘制无设置状态（默认筛选图标）
    static drawNoSetting(ctx: UniverRenderingContext2D, size: number, fgColor: string, bgColor: string): void {
        // 绘制背景
        Rect.drawWith(ctx, { radius: 2, width: BUTTON_VIEWPORT, height: BUTTON_VIEWPORT, fill: bgColor });
        
        // 绘制三条横线
        ctx.moveTo(3, 4); ctx.lineTo(13, 4);
        ctx.moveTo(4.5, 8); ctx.lineTo(11.5, 8);
        ctx.moveTo(6, 12); ctx.lineTo(10, 12);
        ctx.stroke();
    }

    // 根据路径数据绘制图标
    static drawIconByPath(ctx: UniverRenderingContext2D, pathData: string[], fgColor: string, bgColor: string): void {
        // 绘制背景
        Rect.drawWith(ctx, { radius: 2, width: BUTTON_VIEWPORT, height: BUTTON_VIEWPORT, fill: bgColor });
        
        // 绘制路径图标
        pathData.forEach((d) => {
            const path = new Path2D(d);
            ctx.fillStyle = fgColor;
            ctx.fill(path, 'evenodd');
        });
    }
}
````
</augment_code_snippet>

## 3. 事件绑定机制

### 3.1 事件流程

1. **鼠标事件监听**：在按钮形状构造函数中绑定事件
2. **点击处理**：检查面板状态，防止重复打开
3. **命令执行**：通过 CommandService 执行操作
4. **面板显示**：打开对应的筛选面板

### 3.2 关键事件处理

<augment_code_snippet path="packages/sheets-filter-ui/src/views/widgets/filter-button.shape.ts" mode="EXCERPT">
````typescript
onPointerDown(evt: IPointerEvent | IMouseEvent): void {
    // 忽略右键点击
    if (evt.button === 2) return;
    
    const { col, unitId, subUnitId } = this._filterParams!;
    const opened = this._contextService.getContextValue(FILTER_PANEL_OPENED_KEY);
    
    // 防止重复打开面板
    if (opened || !this._commandService.hasCommand(OpenFilterPanelOperation.id)) {
        return;
    }
    
    // 延迟执行，避免事件冲突
    setTimeout(() => {
        this._commandService.executeCommand(OpenFilterPanelOperation.id, {
            unitId, subUnitId, col
        });
    }, 200);
}

// 鼠标悬停效果
onPointerEnter(): void {
    this._hovered = true;
    this.makeDirty(true);  // 标记需要重绘
}

onPointerLeave(): void {
    this._hovered = false;
    this.makeDirty(true);
}
````
</augment_code_snippet>

## 4. 位置计算和布局

### 4.1 图标位置计算

筛选图标始终位于单元格的右上角：

```typescript
// 计算图标起始位置
const iconStartX = endX - FILTER_ICON_SIZE - FILTER_ICON_PADDING;
const iconStartY = endY - FILTER_ICON_SIZE - FILTER_ICON_PADDING;
```

### 4.2 空间预留机制

通过拦截器为筛选按钮预留空间，避免与单元格内容重叠：

```typescript
cell.fontRenderExtension = {
    ...cell?.fontRenderExtension,
    rightOffset: FILTER_ICON_SIZE,  // 预留16px空间
};
```

## 5. 渲染优化

### 5.1 条件渲染
- 只在有筛选范围时渲染图标
- 单元格太小时跳过渲染
- 使用脏标记机制优化重绘

### 5.2 事件优化
- 使用节流机制避免频繁重绘
- 延迟执行命令避免事件冲突
- 合理的 Z-Index 层级管理

## 6. 主要常量定义

```typescript
export const FILTER_ICON_SIZE = 16;           // 图标大小
export const FILTER_ICON_PADDING = 1;         // 图标边距
const SHEETS_FILTER_BUTTON_Z_INDEX = 5000;    // 图标层级
const BUTTON_VIEWPORT = 16;                    // 按钮视口大小
```

这套筛选图标系统通过精心设计的渲染控制器、事件处理机制和绘制逻辑，实现了高效、响应式的用户交互体验。
